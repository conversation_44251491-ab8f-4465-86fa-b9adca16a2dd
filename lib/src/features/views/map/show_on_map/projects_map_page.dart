import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:widget_to_marker/widget_to_marker.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../projects/project_details_page.dart';

class ProjectsMapPage extends StatefulWidget {
  final double? initialLat;
  final double? initialLng;

  const ProjectsMapPage({
    super.key,
    this.initialLat,
    this.initialLng,
  });

  @override
  _ProjectsMapPageState createState() => _ProjectsMapPageState();
}

class _ProjectsMapPageState extends State<ProjectsMapPage>
    with TickerProviderStateMixin {
  bool isload = false;
  int? mainid;
  String? name, description, label, image, type;
  var price;
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  late LatLng _mainLocation;

  final currencyController = CurrencyController();

  double? lat;
  double? lng;
  String? lang;
  TextEditingController searchcontroller = TextEditingController();
  List<String> searchNames = [];
  ContentController contentController = ContentController();

  VideoModel? video;

  int? projectLocationValue;
  int? projectPropertyStatusValue;
  String? projectPaymentMethodValue;

  LatLng? navigatedLocation;

  @override
  void initState() {
    super.initState();

    currencyController.getcuurentcurrency(context);

    // Use provided coordinates or default to Dubai
    lat = widget.initialLat ?? 25.229279238648324;
    lng = widget.initialLng ?? 55.270265778344175;
    _mainLocation = LatLng(lat!, lng!);

    getmarkers(type: AppConstants.projectsId.toString(), lat: lat!, lng: lng!);

    contentController.getlocations();
    contentController.getPropertyStatuses();
  }

  Future<void> getmarkers({
    required double lat,
    required double lng,
    required String type,
    String? key,
  }) async {
    final value = await Api.getmainCategory(
      1,
      100,
      key ?? '',
      AppConstants.projectsId.toString(),
    );

    // final value = await Api.getmainCategorySearch(
    //   lat,
    //   lng,
    //   '10',
    //   key,
    //   null, // Projects don't use bedroom filters
    //   projectLocationValue,
    // );

    if (value.category.isNotEmpty) {
      setState(() {
        searchNames.clear();
        _markers.clear();
      });

      // If specific coordinates are provided, show only that project
      bool showSpecificProject =
          widget.initialLat != null && widget.initialLng != null;

      for (var i = 0; i < value.category.length; i++) {
        if (value.category[i].latitude != null &&
            value.category[i].longitude != null) {
          LatLng location =
              LatLng(value.category[i].latitude!, value.category[i].longitude!);

          // If showing specific project, only add marker if coordinates match
          if (showSpecificProject) {
            double latDiff = (location.latitude - widget.initialLat!).abs();
            double lngDiff = (location.longitude - widget.initialLng!).abs();
            // Allow small tolerance for coordinate matching (0.001 degrees ≈ 100m)
            if (latDiff > 0.001 || lngDiff > 0.001) {
              continue; // Skip this project if coordinates don't match
            }
          }

          searchNames.add(value.category[i].name!);
          navigatedLocation = location;

          final marker = Marker(
            onTap: () {
              Navigator.of(context).push(MaterialPageRoute(
                builder: (BuildContext context) => ProjectDetailsPage(
                  project: value.category[i],
                ),
              ));
            },
            markerId: MarkerId(location.toString()),
            position: location,
            icon: kIsWeb
                ? BitmapDescriptor.defaultMarker
                : await ProjectIcon().toBitmapDescriptor(
                    logicalSize: const Size(100, 100),
                    imageSize: const Size(100, 100),
                  ),
          );

          setState(() {
            _markers.add(marker);
          });
        }
      }
    }
  }

  Future<void> onSearchTextChanged(String text) async {
    if (text.isEmpty) {
      await getmarkers(
          type: AppConstants.projectsId.toString(), lat: lat!, lng: lng!);
    } else {
      await getmarkers(
          type: AppConstants.projectsId.toString(),
          lat: lat!,
          lng: lng!,
          key: text);
    }
  }

  static const double zoomNumber = 11;
  double zoom = zoomNumber;

  @override
  Widget build(BuildContext context) {
    final searchField = widget.initialLat != null && widget.initialLng != null
        ? const SizedBox.shrink()
        : Positioned(
            top: 20,
            left: 20,
            right: 20,
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ]),
              child: TypeAheadField(
                  textFieldConfiguration: TextFieldConfiguration(
                    controller: searchcontroller,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        onSearchTextChanged(value);
                        FocusScope.of(context).requestFocus(FocusNode());
                      }
                    },
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.only(
                        top: isEng(context) ? 15 : 20,
                        left: 16,
                        right: 16,
                      ),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xff8B959E),
                        size: 20,
                      ),
                      hintText: AppLocalizations.of(context)
                          .translate('Search projects'),
                      hintStyle: const TextStyle(
                        color: Color(0xff8B959E),
                        fontSize: 14,
                      ),
                    ),
                  ),
                  errorBuilder: (context, error) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text("$error"),
                      ),
                    );
                  },
                  loadingBuilder: (context) {
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  },
                  suggestionsCallback: (pattern) {
                    return searchNames.where((element) =>
                        element.toLowerCase().contains(pattern.toLowerCase()));
                  },
                  noItemsFoundBuilder: (context) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Text(
                          AppLocalizations.of(context)
                              .translate('No projects found'),
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ),
                    );
                  },
                  itemBuilder: (context, suggestion) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      child: Text(
                        suggestion.toString(),
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  },
                  onSuggestionSelected: (suggestion) async {
                    await onSearchTextChanged(suggestion);

                    myMapController!.animateCamera(
                      CameraUpdate.newLatLng(
                          navigatedLocation ?? _mainLocation),
                    );

                    zoom = zoomNumber;
                    searchcontroller.text = suggestion;

                    setState(() {});
                  }),
            ));

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: primaryColor,
        centerTitle: true,
        elevation: 0,
        title: Text(
          AppLocalizations.of(context).translate('Projects Map'),
          style: TextStyle(
            fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _mainLocation,
                zoom: zoom,
              ),
              markers: Set<Marker>.of(_markers),
              mapType: MapType.normal,
              onMapCreated: (controller) {
                setState(() {
                  myMapController = controller;
                });
              },
              onTap: (LatLng latLng) async {}),
        ),
        searchField,
      ]),
      bottomNavigationBar: CustomBottomNavgationBar(2),
    ));
  }
}

class ProjectIcon extends StatelessWidget {
  const ProjectIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Image.asset(
          kIsWeb ? 'assets/web_holiday_icon.png' : 'assets/holiday_icon.png',
          width: 40,
          height: 40,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
