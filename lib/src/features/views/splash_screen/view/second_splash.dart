import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';

import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/utils/dynamic_links.dart';
import '../../../models/model_select.dart';
import '../../home/<USER>';

class SecondSplash extends StatefulWidget {
  const SecondSplash({super.key});

  @override
  _SecondSplash createState() => _SecondSplash();
}

class _SecondSplash extends State<SecondSplash> {
  @override
  void initState() {
    super.initState();
    if (!kIsWeb) {
      DynamicLinkHandler.initDynamicLink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            body: Stack(
      children: [
        Image.asset(
          'assets/splash.png',
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          fit: BoxFit.cover,
        ),
        Positioned(
          bottom: 5,
          left: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Text(
                    AppLocalizations.of(context).translate('Explore New'),
                    style: const TextStyle(fontSize: 26, color: Colors.white),
                  )),
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Text(
                    AppLocalizations.of(context).translate('Holiday Homes'),
                    style: const TextStyle(fontSize: 26, color: Colors.white),
                    textDirection: TextDirection.ltr,
                  )),
              const SizedBox(
                height: 20,
              ),
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Text(
                    AppLocalizations.of(context).translate(
                        'Check Duabi s best landmarks,attractions,and'),
                    style: const TextStyle(fontSize: 12, color: Colors.white),
                  )),
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Text(
                    AppLocalizations.of(context)
                        .translate('top places of interest'),
                    style: const TextStyle(fontSize: 12, color: Colors.white),
                  )),
              // Spacer(),
              const SizedBox(
                height: 30,
              ),
              Container(
                  padding:
                      const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  child: Row(
                    children: [
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).pushReplacement(
                                MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        const Home()));
                          },
                          child: Container(
                              width: MediaQuery.of(context).size.width * 0.5,
                              height: 50,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: const Color(0xffD8B77F)),
                              child: Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('Get Started'),
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                              ))),
                      const SizedBox(
                        width: 20,
                      ),
                      GestureDetector(
                        onTap: () => changeLanguage(),
                        child: Container(
                            width: MediaQuery.of(context).size.width * 0.32,
                            height: 50,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                color: Colors.white),
                            child: Center(
                              child: Text(
                                isEnglish(context)
                                    ? ' 🇦🇪 اللغة العربية'
                                    : 'English 🇺🇸',
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            )),
                      )
                    ],
                  )),
            ],
          ),
        )
      ],
    )));
  }

  void changeLanguage() async {
    var appLanguage = Provider.of<AppLanguage>(context, listen: false);

    final langList = [
      ModelSelect('English', isEnglish(context) ? true : false),
      ModelSelect('العربية', !isEnglish(context) ? true : false)
    ];

    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      for (int i = 0; i < langList.length; i++) {
        langList[i].check = false;
      }
      var languagecode = prefs.getString('language_code');
      if (languagecode == "ar") {
        appLanguage.changeLanguage(const Locale("en"));
        langList[0].check = true;
      } else {
        appLanguage.changeLanguage(const Locale("ar"));
        langList[1].check = true;
      }
    });

    for (var property in properties) {
      videoPlayerController.removeWhere((key, value) => key == property.id);
    }

    Phoenix.rebirth(context);

    // Provider.of<AppLanguage>(context, listen: false).changeLanguage(
    //     isEnglish(context) ? const Locale('ar') : const Locale('en'));

    // Navigator.of(context).push(MaterialPageRoute(
    //     builder: (BuildContext context) => const FirstSplash()));
  }
}
